/**
 * User utility functions for working with Clerk user data
 */

/**
 * Get user initials from a <PERSON> user object
 *
 * @param user - The Clerk user object from useUser() hook
 * @param fallback - Optional fallback string if initials can't be generated
 * @returns A string with the user's initials (usually 2 characters)
 */
export function getUserInitials(
  user:
    | {
        fullName?: string | null;
        firstName?: string | null;
        lastName?: string | null;
        emailAddresses?: Array<{ emailAddress: string }> | null;
      }
    | null
    | undefined,
  fallback: string = 'CN',
): string {
  if (!user) return fallback;

  // Try to use fullName first
  if (user.fullName) {
    const nameParts = user.fullName.trim().split(/\s+/);
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
    } else if (nameParts.length === 1 && nameParts[0].length >= 1) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }
  }

  // Try to use firstName and lastName
  if (user.firstName && user.lastName) {
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  }

  // Try to use just firstName or lastName
  if (user.firstName && user.firstName.length > 0) {
    return user.firstName.substring(0, 2).toUpperCase();
  }

  if (user.lastName && user.lastName.length > 0) {
    return user.lastName.substring(0, 2).toUpperCase();
  }

  // Try to use email
  if (
    user.emailAddresses &&
    Array.isArray(user.emailAddresses) &&
    user.emailAddresses.length > 0
  ) {
    const email = user.emailAddresses[0].emailAddress;
    if (email) {
      const username = email.split('@')[0];
      return username.substring(0, 2).toUpperCase();
    }
  }

  // If all else fails, return the fallback
  return fallback;
}
