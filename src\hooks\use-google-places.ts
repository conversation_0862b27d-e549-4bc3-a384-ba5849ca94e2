import { useEffect, useState } from 'react';

interface GooglePlaceSuggestion {
  placePrediction: {
    text: {
      text: string;
    };
    placeId: string;
  };
}

interface GoogleAddressComponent {
  longText: string;
  shortText: string;
  types: string[];
}

export interface PlaceResult {
  address: string;
  placeId: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  components: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export function useGooglePlaces() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;

    console.log('Google Places API Key:', apiKey ? 'Present' : 'Missing');

    if (!apiKey) {
      console.warn(
        'Google Places API key not found. Please add NEXT_PUBLIC_GOOGLE_PLACES_API_KEY to your environment variables.',
      );
      return;
    }

    console.log('Google Places API (New) ready - using REST API');
    setIsLoaded(true);
  }, []);

  const searchPlaces = async (input: string): Promise<PlaceResult[]> => {
    if (!isLoaded || !input) {
      console.log('Google Places API not loaded or empty input');
      return [];
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;
    if (!apiKey) {
      console.error('API key not available');
      return [];
    }

    console.log('Searching for places with input:', input);

    try {
      const response = await fetch(
        'https://places.googleapis.com/v1/places:autocomplete',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask':
              'suggestions.placePrediction.place,suggestions.placePrediction.placeId,suggestions.placePrediction.text',
          },
          body: JSON.stringify({
            input,
            locationBias: {
              circle: {
                center: {
                  latitude: 14.5995, // Philippines center
                  longitude: 120.9842,
                },
                radius: 50000.0, // 50km radius
              },
            },
            includedRegionCodes: ['ph'], // Restrict to Philippines
            languageCode: 'en',
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Google Places API (New) response:', data);

      if (data.suggestions) {
        const results: PlaceResult[] = data.suggestions
          .filter(
            (suggestion: GooglePlaceSuggestion) => suggestion.placePrediction,
          )
          .map((suggestion: GooglePlaceSuggestion) => ({
            address: suggestion.placePrediction.text.text,
            placeId: suggestion.placePrediction.placeId,
            coordinates: { lat: 0, lng: 0 }, // Will be filled by getPlaceDetails
            components: {},
          }));

        console.log('Mapped results:', results);
        return results;
      }

      return [];
    } catch (error) {
      console.error('Error fetching places:', error);
      return [];
    }
  };

  const getPlaceDetails = async (
    placeId: string,
  ): Promise<PlaceResult | null> => {
    if (!isLoaded) return null;

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;
    if (!apiKey) {
      console.error('API key not available');
      return null;
    }

    try {
      const response = await fetch(
        `https://places.googleapis.com/v1/places/${placeId}`,
        {
          method: 'GET',
          headers: {
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask': 'formattedAddress,location,addressComponents',
          },
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const place = await response.json();
      console.log('Place details response:', place);

      const components: PlaceResult['components'] = {};

      place.addressComponents?.forEach((component: GoogleAddressComponent) => {
        const types = component.types;
        if (types.includes('street_number') || types.includes('route')) {
          components.street =
            (components.street || '') + ' ' + component.longText;
        } else if (types.includes('locality')) {
          components.city = component.longText;
        } else if (types.includes('administrative_area_level_1')) {
          components.state = component.shortText;
        } else if (types.includes('country')) {
          components.country = component.longText;
        } else if (types.includes('postal_code')) {
          components.postalCode = component.longText;
        }
      });

      return {
        address: place.formattedAddress || '',
        placeId,
        coordinates: {
          lat: place.location?.latitude || 0,
          lng: place.location?.longitude || 0,
        },
        components,
      };
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  };

  return {
    isLoaded,
    searchPlaces,
    getPlaceDetails,
  };
}
