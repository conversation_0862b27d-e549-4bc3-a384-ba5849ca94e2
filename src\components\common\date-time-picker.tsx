'use client';

import * as React from 'react';
import { set } from 'date-fns';

import { cn } from '@/lib/utils';
import { DatePicker } from './date-picker';
import { TimePicker } from './time-picker';

export interface DateTimePickerProps {
  value: Date | undefined;
  onChange: (value: Date | undefined) => void;
  dateLabel?: string;
  timeLabel?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  dateFormat?: string;
  timeFormat?: string;
  timeStep?: number;
  clearable?: boolean;
  disablePastDates?: boolean;
}

export function DateTimePicker({
  value,
  onChange,
  dateLabel = 'Date',
  timeLabel = 'Time',
  disabled = false,
  error,
  className,
  dateFormat,
  timeFormat,
  timeStep = 30,
  disablePastDates = false,
}: DateTimePickerProps) {
  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (!date) {
      onChange(undefined);
      return;
    }

    if (value) {
      // Preserve the time from the existing value
      const newDateTime = set(date, {
        hours: value.getHours(),
        minutes: value.getMinutes(),
        seconds: 0,
        milliseconds: 0,
      });
      onChange(newDateTime);
    } else {
      // Set default time to current time
      const now = new Date();
      const newDateTime = set(date, {
        hours: now.getHours(),
        minutes: now.getMinutes(),
        seconds: 0,
        milliseconds: 0,
      });
      onChange(newDateTime);
    }
  };

  // Handle time change
  const handleTimeChange = (time: Date | undefined) => {
    if (!time) {
      onChange(undefined);
      return;
    }

    if (value) {
      // Preserve the date from the existing value
      const newDateTime = set(value, {
        hours: time.getHours(),
        minutes: time.getMinutes(),
        seconds: 0,
        milliseconds: 0,
      });
      onChange(newDateTime);
    } else {
      // Set default date to today
      const today = new Date();
      const newDateTime = set(today, {
        hours: time.getHours(),
        minutes: time.getMinutes(),
        seconds: 0,
        milliseconds: 0,
      });
      onChange(newDateTime);
    }
  };

  return (
    <div
      className={cn(
        'flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0',
        className,
      )}
    >
      <DatePicker
        date={value}
        onDateChange={handleDateChange}
        label={dateLabel}
        disabled={disabled}
        error={error}
        format={dateFormat}
        containerClassName="flex-1"
        disablePastDates={disablePastDates}
      />
      <TimePicker
        time={value}
        onTimeChange={handleTimeChange}
        label={timeLabel}
        disabled={disabled}
        error={error}
        format={timeFormat}
        step={timeStep}
        containerClassName="flex-1"
      />
    </div>
  );
}
