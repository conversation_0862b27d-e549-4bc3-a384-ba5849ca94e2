import React from 'react';

import { Menu } from 'lucide-react';
import Link from 'next/link';
import Logo from '@/components/logo';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { But<PERSON> } from '@/components/ui/button';

export const NavItems = () => {
  return (
    <>
      <nav className="hidden flex-col gap-6 text-lg font-medium md:flex md:flex-row md:items-center md:gap-5 md:text-sm lg:gap-6">
        <Logo />
        {/* text-muted-foreground  or text-foreground*/}
        <Link
          href="#"
          className="text-foreground transition-colors hover:text-foreground tracking-wider"
        >
          Tournaments
        </Link>
      </nav>
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="shrink-0 md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left">
          <nav className="grid gap-6 text-lg font-medium">
            <Logo />
            <Link href="#" className="hover:text-foreground">
              Tournaments
            </Link>
          </nav>
        </SheetContent>
      </Sheet>
    </>
  );
};
