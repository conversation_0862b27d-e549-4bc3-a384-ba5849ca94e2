'use client';

import * as React from 'react';
import { ChevronDownIcon } from 'lucide-react';
import { Combobox, ComboboxOption } from './combobox';

export interface TimeControlValue {
  time: string; // in minutes
  increment: string; // in seconds
}

export interface TimeControlProps {
  value?: TimeControlValue;
  onValueChange: (value: TimeControlValue) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
}

// Generate time options from 3 to 90 minutes
const generateTimeOptions = (): ComboboxOption[] => {
  const options: ComboboxOption[] = [];

  // Add common time controls first
  const commonTimes = [3, 5, 10, 15, 30, 45, 60, 90];

  for (const time of commonTimes) {
    options.push({
      value: time.toString(),
      label: `${time} minute${time !== 1 ? 's' : ''}`,
    });
  }

  // Add remaining times
  for (let i = 1; i <= 90; i++) {
    if (!commonTimes.includes(i)) {
      options.push({
        value: i.toString(),
        label: `${i} minute${i !== 1 ? 's' : ''}`,
      });
    }
  }

  // Sort by numeric value
  return options.sort((a, b) => parseInt(a.value) - parseInt(b.value));
};

// Generate increment options from 0 to 60 seconds
const generateIncrementOptions = (): ComboboxOption[] => {
  const options: ComboboxOption[] = [];

  for (let i = 0; i <= 60; i++) {
    options.push({
      value: i.toString(),
      label: `${i} second${i !== 1 ? 's' : ''}`,
    });
  }

  return options;
};

export function TimeControl({
  value,
  onValueChange,
  disabled = false,
  error,
  className,
  containerClassName,
}: TimeControlProps) {
  const timeOptions = React.useMemo(() => generateTimeOptions(), []);
  const incrementOptions = React.useMemo(() => generateIncrementOptions(), []);

  const handleTimeChange = (time: string) => {
    onValueChange({
      time,
      increment: value?.increment || '0',
    });
  };

  const handleIncrementChange = (increment: string) => {
    onValueChange({
      time: value?.time || '10',
      increment,
    });
  };

  // Create chevron down icons
  const chevronIcon = (
    <ChevronDownIcon className="h-4 w-4 opacity-50 flex-shrink-0" />
  );

  return (
    <div className={containerClassName}>
      <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
        <Combobox
          value={value?.time}
          onValueChange={handleTimeChange}
          options={timeOptions}
          label="Time Control"
          searchPlaceholder="Search time..."
          emptyMessage="No time found."
          disabled={disabled}
          error={error}
          className={className}
          icon={chevronIcon}
          iconPosition="right"
          searchable={true}
        />

        <Combobox
          value={value?.increment}
          onValueChange={handleIncrementChange}
          options={incrementOptions}
          label="Increment"
          searchPlaceholder="Search increment..."
          emptyMessage="No increment found."
          disabled={disabled}
          className={className}
          icon={chevronIcon}
          iconPosition="right"
          searchable={true}
        />
      </div>
    </div>
  );
}
