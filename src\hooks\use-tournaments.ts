import { useMutation, useQuery } from 'convex/react';
import { api } from '@convex/_generated/api';
import { Id } from '@convex/_generated/dataModel';

// Hook for tournament operations
export const useTournaments = () => {
  // Queries
  const userTournaments = useQuery(api.tournaments.getUserTournaments);
  const publishedTournaments = useQuery(
    api.tournaments.getPublishedTournaments,
  );

  // Mutations
  const createDraftTournament = useMutation(
    api.tournaments.createDraftTournament,
  );
  const createTournament = useMutation(api.tournaments.createTournament);
  const updateTournamentBasics = useMutation(
    api.tournaments.updateTournamentBasics,
  );
  const updateTournamentSchedule = useMutation(
    api.tournaments.updateTournamentSchedule,
  );
  const updateTournamentOrganizers = useMutation(
    api.tournaments.updateTournamentOrganizers,
  );
  const updateTournamentSettings = useMutation(
    api.tournaments.updateTournamentSettings,
  );
  const updateTournamentCategories = useMutation(
    api.tournaments.updateTournamentCategories,
  );
  const updateTournamentSponsors = useMutation(
    api.tournaments.updateTournamentSponsors,
  );
  const updateTournamentStatus = useMutation(
    api.tournaments.updateTournamentStatus,
  );
  const deleteTournament = useMutation(api.tournaments.deleteTournament);

  return {
    // Queries
    userTournaments,
    publishedTournaments,

    // Mutations
    createDraftTournament,
    createTournament,
    updateTournamentBasics,
    updateTournamentSchedule,
    updateTournamentOrganizers,
    updateTournamentSettings,
    updateTournamentCategories,
    updateTournamentSponsors,
    updateTournamentStatus,
    deleteTournament,
  };
};

// Hook for getting a specific tournament
export const useTournament = (tournamentId: Id<'tournaments'> | undefined) => {
  const tournament = useQuery(
    api.tournaments.getTournament,
    tournamentId ? { tournamentId } : 'skip',
  );

  return tournament;
};

// Hook for user operations
export const useUser = () => {
  const currentUser = useQuery(api.users.getCurrentUser);
  const createOrUpdateUser = useMutation(api.users.createOrUpdateUser);

  return {
    currentUser,
    createOrUpdateUser,
  };
};

// Hook for file operations
export const useFiles = () => {
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const deleteFile = useMutation(api.files.deleteFile);
  const getFileUrl = useMutation(api.files.getFileUrl);

  return {
    generateUploadUrl,
    deleteFile,
    getFileUrl,
  };
};
