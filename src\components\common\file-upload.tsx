'use client';

import * as React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Images, X, Upload, Edit3 } from 'lucide-react';

export interface FileUploadProps {
  onFilesChange?: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  className?: string;
  disabled?: boolean;
  variant?: 'default' | 'banner';
}

const FileUpload = React.forwardRef<HTMLDivElement, FileUploadProps>(
  (
    {
      onFilesChange,
      accept = 'image/*',
      multiple = true,
      maxFiles = 10,
      className,
      disabled = false,
      variant = 'default',
      ...props
    },
    ref,
  ) => {
    const [isDragOver, setIsDragOver] = React.useState(false);
    const [files, setFiles] = React.useState<File[]>([]);
    const fileInputRef = React.useRef<HTMLInputElement>(null);

    const handleFiles = React.useCallback(
      (newFiles: FileList | null) => {
        if (!newFiles || disabled) return;

        const fileArray = Array.from(newFiles);
        const validFiles = fileArray.filter((file) => {
          if (accept === 'image/*') {
            return file.type.startsWith('image/');
          }
          return true;
        });

        const limitedFiles = multiple
          ? validFiles.slice(0, maxFiles)
          : validFiles.slice(0, 1);

        setFiles(limitedFiles);
        onFilesChange?.(limitedFiles);
      },
      [accept, multiple, maxFiles, onFilesChange, disabled],
    );

    const handleDragOver = React.useCallback(
      (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (!disabled) {
          setIsDragOver(true);
        }
      },
      [disabled],
    );

    const handleDragLeave = React.useCallback(
      (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragOver(false);
      },
      [],
    );

    const handleDrop = React.useCallback(
      (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragOver(false);
        if (!disabled) {
          handleFiles(e.dataTransfer.files);
        }
      },
      [handleFiles, disabled],
    );

    const handleFileInputChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        handleFiles(e.target.files);
      },
      [handleFiles],
    );

    const handleBrowseClick = React.useCallback(() => {
      if (!disabled) {
        fileInputRef.current?.click();
      }
    }, [disabled]);

    const handleRemoveFile = React.useCallback(
      (indexToRemove: number) => {
        const newFiles = files.filter((_, index) => index !== indexToRemove);
        setFiles(newFiles);
        onFilesChange?.(newFiles);
      },
      [files, onFilesChange],
    );

    const handleChangeFiles = React.useCallback(() => {
      if (!disabled) {
        fileInputRef.current?.click();
      }
    }, [disabled]);

    const createImagePreview = React.useCallback((file: File): string => {
      return URL.createObjectURL(file);
    }, []);

    // Cleanup object URLs when component unmounts or files change
    React.useEffect(() => {
      return () => {
        files.forEach((file) => {
          const url = createImagePreview(file);
          URL.revokeObjectURL(url);
        });
      };
    }, [files, createImagePreview]);

    return (
      <div
        ref={ref}
        className={cn(
          'relative w-full transition-colors',
          // Banner variant with image - no border, no padding, fixed height
          variant === 'banner' && files.length > 0
            ? 'rounded-lg overflow-hidden group h-48'
            : // Banner variant without image - dashed border with padding, fixed height
            variant === 'banner' && files.length === 0
            ? 'rounded-lg border-2 border-dashed border-muted-foreground/25 bg-background text-center h-48 flex items-center justify-center p-6'
            : // Default variant
            files.length > 0
            ? 'rounded-lg border-2 border-dashed border-muted-foreground/25 bg-background p-4'
            : 'rounded-lg border-2 border-dashed border-muted-foreground/25 bg-background p-8 text-center',
          isDragOver && !disabled && 'border-primary bg-primary/5',
          disabled && 'opacity-50 cursor-not-allowed',
          !disabled &&
            files.length === 0 &&
            'hover:border-muted-foreground/50 cursor-pointer',
          className,
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={files.length === 0 ? handleBrowseClick : undefined}
        {...props}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {files.length === 0 ? (
          // Upload area when no files selected
          <div
            className={cn(
              'flex flex-col items-center justify-center h-full w-full',
              variant === 'banner' ? 'gap-y-2' : 'space-y-4 p-0',
            )}
          >
            <div className="py-4 flex items-center justify-center w-16 h-16 rounded-lg bg-muted">
              <Images className="w-8 h-8 text-muted-foreground" />
            </div>

            <div className="space-y-1 text-center">
              <h3 className="text-lg font-medium text-foreground">
                Drag and drop
              </h3>
              <p className="text-sm text-muted-foreground">
                {variant === 'banner'
                  ? 'or browse for banner image'
                  : 'or browse for photos'}
              </p>
            </div>

            <Button
              type="button"
              variant="outline"
              size="default"
              disabled={disabled}
              onClick={(e) => {
                e.stopPropagation();
                handleBrowseClick();
              }}
              className="text-muted-foreground border-muted-foreground/30 hover:text-foreground hover:border-muted-foreground/50"
            >
              Browse
            </Button>
          </div>
        ) : variant === 'banner' ? (
          // Banner preview for single image - Facebook-style edit button
          <>
            <Image
              src={createImagePreview(files[0])}
              alt={files[0].name}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors" />

            {/* Facebook-style Edit button */}
            <div className="absolute top-4 right-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    disabled={disabled}
                    className="bg-white/90 hover:bg-white text-black border-white/20 font-medium"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleChangeFiles();
                    }}
                    disabled={disabled}
                    className="cursor-pointer"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(0);
                    }}
                    disabled={disabled}
                    className="cursor-pointer text-destructive focus:text-destructive"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Remove
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {isDragOver && (
              <div className="absolute inset-0 bg-primary/10 border-2 border-primary border-dashed rounded-lg flex items-center justify-center">
                <p className="text-primary font-medium">Drop image here</p>
              </div>
            )}
          </>
        ) : (
          // Grid preview for multiple images (default)
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-foreground">
                Selected photos ({files.length})
              </p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                disabled={disabled}
                onClick={(e) => {
                  e.stopPropagation();
                  handleChangeFiles();
                }}
                className="flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Change
              </Button>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="relative group aspect-square rounded-lg overflow-hidden bg-muted"
                >
                  <Image
                    src={createImagePreview(file)}
                    alt={file.name}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors" />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute top-2 right-2 w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(index);
                    }}
                    disabled={disabled}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-2 truncate">
                    {file.name}
                  </div>
                </div>
              ))}
            </div>

            {isDragOver && (
              <div className="absolute inset-0 bg-primary/10 border-2 border-primary border-dashed rounded-lg flex items-center justify-center">
                <p className="text-primary font-medium">Drop files here</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  },
);

FileUpload.displayName = 'FileUpload';

export { FileUpload };
