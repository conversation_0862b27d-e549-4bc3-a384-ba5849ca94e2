import { Button } from '@/components/ui/button';

const Hero = () => {
  return (
    <div className="flex justify-center">
      <div className="my-32 text-center space-y-5 container mx-auto flex flex-col items-center justify-center ">
        <h1 className=" font-display text-4xl lg:text-5xl font-semibold tracking-wider text-foreground/80 text-center">
          Tournament management made easy
        </h1>
        <h2 className="text-accent-foreground/80">
          Leave behind the days of manual player listing, payment validation,
          and data entry typos.
        </h2>
        <Button
          asChild
          className="md:p-6 max-w-[200px] w-full text-base md:text-lg"
        >
          Get Started
        </Button>
      </div>
    </div>
  );
};

export default Hero;
