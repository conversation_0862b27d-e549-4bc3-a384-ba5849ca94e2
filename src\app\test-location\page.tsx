'use client';

import { useState } from 'react';
import {
  LocationField,
  LocationResult,
} from '@/components/common/location-field';

export default function TestLocationPage() {
  const [location, setLocation] = useState<string>('');
  const [selectedLocation, setSelectedLocation] =
    useState<LocationResult | null>(null);

  return (
    <div className="container mx-auto max-w-2xl py-8">
      <h1 className="text-2xl font-bold mb-6">Google Places Test</h1>

      <div className="space-y-4">
        <LocationField
          label="Test Location Field"
          value={location}
          onChange={setLocation}
          onLocationSelect={(location) => {
            setSelectedLocation(location);
            console.log('Selected location:', location);
          }}
        />

        {selectedLocation && (
          <div className="mt-4 p-4 bg-gray-100 rounded-md">
            <h3 className="font-semibold mb-2">Selected Location:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(selectedLocation, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>Instructions:</p>
          <ul className="list-disc list-inside mt-2">
            <li>Type at least 3 characters to see suggestions</li>
            <li>Check the browser console for debug messages</li>
            <li>
              Try searching for places like &ldquo;Manila&rdquo;,
              &ldquo;Makati&rdquo;, etc.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
