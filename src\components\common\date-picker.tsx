'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { BasePicker } from './base-picker';

export interface DatePickerProps {
  date: Date | undefined;
  onDateChange: (date: Date | undefined) => void;
  label: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
  calendarClassName?: string;
  format?: string;
  clearable?: boolean;
  disablePastDates?: boolean;
}

export function DatePicker({
  date,
  onDateChange,
  label,
  disabled = false,
  error,
  className,
  containerClassName,
  calendarClassName,
  format: dateFormat = 'PPP', // Default format: "Apr 29, 2024"
  disablePastDates = false,
}: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  // Format the date for display
  const formattedDate = date ? format(date, dateFormat) : '';

  // Create the calendar icon with consistent styling
  const calendarIcon = (
    <CalendarIcon className="h-4 w-4 opacity-50 flex-shrink-0" />
  );

  // Create a function to disable past dates if disablePastDates is true
  const isPastDate = (date: Date) => {
    if (!disablePastDates) return false;

    // Get today's date at the start of the day (midnight)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Return true if the date is before today (to disable it)
    return date < today;
  };

  // Handle date selection and close popover
  const handleDateSelect = (selectedDate: Date | undefined) => {
    onDateChange(selectedDate);
    if (selectedDate) {
      setIsOpen(false);
    }
  };

  return (
    <BasePicker
      label={label}
      value={formattedDate}
      icon={calendarIcon}
      disabled={disabled}
      error={error}
      className={className}
      containerClassName={containerClassName}
      contentClassName="p-0"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <Calendar
        mode="single"
        selected={date}
        onSelect={handleDateSelect}
        initialFocus
        className={cn('rounded-md border', calendarClassName)}
        disabled={isPastDate}
      />
    </BasePicker>
  );
}
