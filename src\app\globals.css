@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
}

:root {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(0.98 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(0.98 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.56 0.24 261.16);
  --primary-foreground: oklch(.98 0 0);
  --secondary: oklch(0.9 0.05 260);
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.89 0.01 260);
  --muted-foreground: oklch(0.5 0.02 260);
  --accent: oklch(0.9 0.05 260);
  --accent-foreground: oklch(0.15 0 0);
  --border: oklch(0.85 0.02 260);
  --input: oklch(0.85 0.02 260);
  --ring: oklch(0.6 0.2 264);
  --destructive: oklch(0.6 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --background: oklch(0.15 0 0);
  --foreground: oklch(0.98 0 0);
  --card: oklch(0.15 0 0);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.15 0 0);
  --popover-foreground: oklch(0.98 0 0);
  --primary: oklch(0.6 0.2 264);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.25 0.1 260);
  --secondary-foreground: oklch(0.98 0 0);
  --muted: oklch(0.22 0.08 260);
  --muted-foreground: oklch(0.6 0.1 260);
  --accent: oklch(0.25 0.1 260);
  --accent-foreground: oklch(0.98 0 0);
  --border: oklch(0.3 0.05 260);
  --input: oklch(0.3 0.05 260);
  --ring: oklch(0.5 0.2 264);
  --destructive: oklch(0.5 0.22 25);
  --destructive-foreground: oklch(0.98 0 0);
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}