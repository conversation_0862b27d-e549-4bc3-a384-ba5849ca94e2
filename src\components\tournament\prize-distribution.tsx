'use client';

import * as React from 'react';
import { Input } from '@/components/common/input';
import { Button } from '@/components/ui/button';
import { Plus, Minus, Trophy } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface PrizePlace {
  place: number;
  amount: string;
}

export interface PrizeDistributionProps {
  prizes: PrizePlace[];
  onPrizesChange: (prizes: PrizePlace[]) => void;
  maxPlaces?: number;
  className?: string;
}

const getOrdinalSuffix = (num: number): string => {
  const j = num % 10;
  const k = num % 100;
  if (j === 1 && k !== 11) return 'st';
  if (j === 2 && k !== 12) return 'nd';
  if (j === 3 && k !== 13) return 'rd';
  return 'th';
};

export function PrizeDistribution({
  prizes,
  onPrizesChange,
  maxPlaces = 10,
  className,
}: PrizeDistributionProps) {
  const addPrizePlace = () => {
    if (prizes.length >= maxPlaces) return;

    const nextPlace =
      prizes.length > 0 ? Math.max(...prizes.map((p) => p.place)) + 1 : 1;
    const newPrize: PrizePlace = {
      place: nextPlace,
      amount: '',
    };
    onPrizesChange([...prizes, newPrize]);
  };

  const removePrizePlace = (place: number) => {
    if (prizes.length <= 1) return; // Keep at least one prize place
    onPrizesChange(prizes.filter((p) => p.place !== place));
  };

  const updatePrizeAmount = (place: number, amount: string) => {
    onPrizesChange(
      prizes.map((p) => (p.place === place ? { ...p, amount } : p)),
    );
  };

  // Sort prizes by place
  const sortedPrizes = [...prizes].sort((a, b) => a.place - b.place);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="space-y-3">
        {sortedPrizes.map((prize) => (
          <div key={prize.place}>
            <Input
              label={`${prize.place}${getOrdinalSuffix(
                prize.place,
              )} Place Prize (₱)`}
              type="number"
              value={prize.amount}
              onChange={(e) => updatePrizeAmount(prize.place, e.target.value)}
              placeholder="Enter amount"
              min="0"
              step="1"
              icon={<Trophy className="h-4 w-4" />}
              rightAction={
                prizes.length > 1 ? (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removePrizePlace(prize.place)}
                    className="text-destructive/50 hover:text-destructive hover:bg-destructive/10 h-6 w-6 p-0"
                    title={`Remove ${prize.place}${getOrdinalSuffix(
                      prize.place,
                    )} place`}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                ) : undefined
              }
            />
          </div>
        ))}
      </div>

      {prizes.length < maxPlaces && (
        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addPrizePlace}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Prize Place
          </Button>
        </div>
      )}

      {prizes.length >= maxPlaces && (
        <p className="text-xs text-muted-foreground text-center">
          Maximum of {maxPlaces} prize places reached
        </p>
      )}
    </div>
  );
}
