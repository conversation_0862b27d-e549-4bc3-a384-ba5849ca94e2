'use client';

import { Button } from '@/components/ui/button';
import { useTournamentForm } from '@/hooks/use-tournament-form';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

interface CreateTournamentIntegratedProps {
  onSuccess?: () => void;
}

export function CreateTournamentIntegrated({ onSuccess }: CreateTournamentIntegratedProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  const {
    formState,
    setFormState,
    currentTournamentId,
    isLoading,
    isSaving,
    lastSaved,
    autoSave,
    publishTournament,
  } = useTournamentForm();

  // Helper function to update form state
  const updateFormState = (updates: Partial<typeof formState>) => {
    setFormState(prev => ({ ...prev, ...updates }));
  };

  const handlePublish = async () => {
    try {
      const success = await publishTournament();
      if (success) {
        onSuccess?.();
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error publishing tournament:', error);
    }
  };

  const handleSaveDraft = async () => {
    try {
      await autoSave();
      toast({
        title: 'Draft Saved',
        description: 'Your tournament has been saved as a draft.',
      });
    } catch (error) {
      console.error('Error saving draft:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Auto-save indicator */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {isSaving && 'Saving...'}
          {lastSaved && !isSaving && `Last saved: ${lastSaved.toLocaleTimeString()}`}
          {currentTournamentId && !lastSaved && 'Draft created'}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            disabled={isSaving}
          >
            Save Draft
          </Button>
          <Button
            onClick={handlePublish}
            disabled={isLoading || isSaving}
          >
            {isLoading ? 'Publishing...' : 'Publish Tournament'}
          </Button>
        </div>
      </div>

      {/* Form fields - example of how to integrate */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Tournament Name</label>
          <input
            type="text"
            value={formState.tournamentName}
            onChange={(e) => updateFormState({ tournamentName: e.target.value })}
            className="w-full p-2 border rounded-md"
            placeholder="Enter tournament name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Description</label>
          <textarea
            value={formState.description}
            onChange={(e) => updateFormState({ description: e.target.value })}
            className="w-full p-2 border rounded-md"
            placeholder="Enter tournament description"
            rows={3}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Location</label>
          <input
            type="text"
            value={formState.location}
            onChange={(e) => updateFormState({ location: e.target.value })}
            className="w-full p-2 border rounded-md"
            placeholder="Enter tournament location"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Time Control (minutes)</label>
            <input
              type="number"
              value={formState.timeControl.time}
              onChange={(e) => updateFormState({ 
                timeControl: { ...formState.timeControl, time: e.target.value }
              })}
              className="w-full p-2 border rounded-md"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Increment (seconds)</label>
            <input
              type="number"
              value={formState.timeControl.increment}
              onChange={(e) => updateFormState({ 
                timeControl: { ...formState.timeControl, increment: e.target.value }
              })}
              className="w-full p-2 border rounded-md"
              min="0"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Start Date & Time</label>
          <input
            type="datetime-local"
            value={formState.startDateTime?.toISOString().slice(0, 16) || ''}
            onChange={(e) => updateFormState({ 
              startDateTime: e.target.value ? new Date(e.target.value) : undefined
            })}
            className="w-full p-2 border rounded-md"
          />
        </div>

        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="showEndDateTime"
            checked={formState.showEndDateTime}
            onChange={(e) => updateFormState({ showEndDateTime: e.target.checked })}
          />
          <label htmlFor="showEndDateTime" className="text-sm font-medium">
            Set end date and time
          </label>
        </div>

        {formState.showEndDateTime && (
          <div>
            <label className="block text-sm font-medium mb-2">End Date & Time</label>
            <input
              type="datetime-local"
              value={formState.endDateTime?.toISOString().slice(0, 16) || ''}
              onChange={(e) => updateFormState({ 
                endDateTime: e.target.value ? new Date(e.target.value) : undefined
              })}
              className="w-full p-2 border rounded-md"
            />
          </div>
        )}

        <div>
          <label className="block text-sm font-medium mb-2">Registration Deadline</label>
          <input
            type="datetime-local"
            value={formState.registrationDeadline?.toISOString().slice(0, 16) || ''}
            onChange={(e) => updateFormState({ 
              registrationDeadline: e.target.value ? new Date(e.target.value) : undefined
            })}
            className="w-full p-2 border rounded-md"
          />
        </div>

        {/* Organizer Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Organizer Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Organizer Name</label>
              <input
                type="text"
                value={formState.organizerName}
                onChange={(e) => updateFormState({ organizerName: e.target.value })}
                className="w-full p-2 border rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Organizer Email</label>
              <input
                type="email"
                value={formState.organizerEmail}
                onChange={(e) => updateFormState({ organizerEmail: e.target.value })}
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Tournament Settings</h3>
          
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="limitParticipants"
              checked={formState.limitParticipants}
              onChange={(e) => updateFormState({ limitParticipants: e.target.checked })}
            />
            <label htmlFor="limitParticipants" className="text-sm font-medium">
              Limit number of participants
            </label>
          </div>

          {formState.limitParticipants && (
            <div>
              <label className="block text-sm font-medium mb-2">Maximum Participants</label>
              <input
                type="number"
                value={formState.maxParticipants}
                onChange={(e) => updateFormState({ maxParticipants: e.target.value })}
                className="w-full p-2 border rounded-md"
                min="1"
              />
            </div>
          )}

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="isFideRated"
              checked={formState.isFideRated}
              onChange={(e) => updateFormState({ isFideRated: e.target.checked })}
            />
            <label htmlFor="isFideRated" className="text-sm font-medium">
              FIDE Rated Tournament
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Equipment Provided By</label>
            <select
              value={formState.equipment}
              onChange={(e) => updateFormState({ equipment: e.target.value })}
              className="w-full p-2 border rounded-md"
            >
              <option value="organizer">Organizer</option>
              <option value="players">Players</option>
              <option value="mixed">Mixed</option>
            </select>
          </div>
        </div>

        {/* Additional Information */}
        <div>
          <label className="block text-sm font-medium mb-2">Additional Information</label>
          <textarea
            value={formState.additionalInfo}
            onChange={(e) => updateFormState({ additionalInfo: e.target.value })}
            className="w-full p-2 border rounded-md"
            placeholder="Any additional information for participants"
            rows={3}
            maxLength={1000}
          />
          <div className="text-xs text-muted-foreground mt-1">
            {formState.additionalInfo.length}/1000 characters
          </div>
        </div>
      </div>

      {/* Debug info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-100 rounded-md">
          <h4 className="font-semibold mb-2">Debug Info:</h4>
          <p className="text-sm">Tournament ID: {currentTournamentId || 'Not created yet'}</p>
          <p className="text-sm">Is Loading: {isLoading.toString()}</p>
          <p className="text-sm">Is Saving: {isSaving.toString()}</p>
          <p className="text-sm">Last Saved: {lastSaved?.toISOString() || 'Never'}</p>
        </div>
      )}
    </div>
  );
}
