'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useIsMobile } from '@/hooks/use-mobile';

export interface BasePickerProps {
  label: string;
  value: string;
  icon: React.ReactNode;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
  contentClassName?: string;
  children: React.ReactNode;
  align?: 'center' | 'start' | 'end';
  iconPosition?: 'left' | 'right';
  onFocus?: () => void;
  onBlur?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function BasePicker({
  label,
  value,
  icon,
  disabled = false,
  error,
  className,
  containerClassName,
  contentClassName,
  children,
  align,
  iconPosition = 'left',
  onFocus,
  onBlur,
  open,
  onOpenChange,
}: BasePickerProps) {
  const [isFocused, setIsFocused] = React.useState(false);
  const [internalOpen, setInternalOpen] = React.useState(false);
  const isMobile = useIsMobile();
  const inputRef = React.useRef<HTMLButtonElement>(null);

  // Use controlled or uncontrolled open state
  const isOpen = open !== undefined ? open : internalOpen;
  const setIsOpen = onOpenChange || setInternalOpen;

  // Determine if the label should float
  const shouldFloat = isFocused || !!value;

  // Handle focus state
  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  return (
    <div className={cn('relative w-full', containerClassName)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <div
          className={cn(
            'group relative w-full rounded-md border border-input transition-all duration-200',
            error ? 'border-destructive ring-destructive/20' : '',
            className,
          )}
        >
          <PopoverTrigger asChild>
            <Button
              ref={inputRef}
              variant="outline"
              size="lg"
              className={cn(
                'peer w-full justify-start bg-transparent px-4 py-3 text-base outline-none transition-all',
                'h-14 rounded-md border-0 shadow-none',
                'disabled:cursor-not-allowed disabled:opacity-50',
                error ? 'text-destructive-foreground' : 'text-foreground',
                !value && 'text-muted-foreground',
              )}
              disabled={disabled}
              onFocus={handleFocus}
              onBlur={handleBlur}
            >
              <div className="flex items-center h-full w-full">
                {iconPosition === 'left' && (
                  <div className="flex items-center justify-center mr-2 flex-shrink-0">
                    {icon}
                  </div>
                )}
                <span className="flex-grow flex items-center mt-3 text-base font-normal min-w-0">
                  {value}
                </span>
                {iconPosition === 'right' && (
                  <div className="flex items-center justify-center ml-auto pl-2 flex-shrink-0">
                    {icon}
                  </div>
                )}
              </div>
            </Button>
          </PopoverTrigger>
          <label
            className={cn(
              'absolute text-muted-foreground transition-all duration-200 cursor-pointer',
              iconPosition === 'left' ? 'left-10' : 'left-4',
              shouldFloat
                ? 'top-2 text-xs'
                : 'top-1/2 -translate-y-1/2 text-base',
              isFocused && !error ? 'text-primary' : '',
              error ? 'text-destructive' : '',
              disabled ? 'cursor-not-allowed' : '',
            )}
            onClick={() => {
              if (!disabled) {
                inputRef.current?.click();
              }
            }}
          >
            {label}
          </label>
          <PopoverContent
            className={cn('w-auto p-0 border-0 shadow-none', contentClassName)}
            align={align || (isMobile ? 'center' : 'start')}
          >
            {children}
          </PopoverContent>
        </div>
      </Popover>
      {error && <p className="text-destructive text-sm mt-1">{error}</p>}
    </div>
  );
}
