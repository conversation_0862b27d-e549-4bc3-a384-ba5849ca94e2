'use client';

import * as React from 'react';
import { MapPin } from 'lucide-react';
import { useGooglePlaces, type PlaceResult } from '@/hooks/use-google-places';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { BasePicker } from './base-picker';

export interface LocationFieldProps {
  label: string;
  value?: string;
  onChange?: (value: string) => void;
  onLocationSelect?: (location: LocationResult) => void;
  error?: string;
  className?: string;
  containerClassName?: string;
  disabled?: boolean;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  hintMessage?: string;
}

export type LocationResult = PlaceResult;

export function LocationField({
  label,
  value = '',
  onChange,
  onLocationSelect,
  error,
  className,
  containerClassName,
  disabled = false,
  searchPlaceholder = 'Search locations...',
  emptyMessage = 'No locations found.',
  hintMessage = 'Type at least 3 characters to see suggestions',
}: LocationFieldProps) {
  const [searchValue, setSearchValue] = React.useState('');
  const [suggestions, setSuggestions] = React.useState<LocationResult[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isOpen, setIsOpen] = React.useState(false);

  // Google Places integration
  const { isLoaded, searchPlaces, getPlaceDetails } = useGooglePlaces();

  // Debounce search to avoid too many API calls
  const debounceTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Handle search value change
  const handleSearchChange = React.useCallback(
    (newSearchValue: string) => {
      setSearchValue(newSearchValue);

      // Clear previous timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Show suggestions when typing
      if (newSearchValue.length > 2) {
        setIsLoading(true);

        // Debounce the search
        debounceTimeoutRef.current = setTimeout(async () => {
          try {
            if (!isLoaded) {
              console.warn(
                'Google Places API not loaded yet, showing fallback',
              );
              // Show a fallback suggestion when API is not loaded
              setSuggestions([
                {
                  address: `${newSearchValue} (Google Places API loading...)`,
                  placeId: 'fallback',
                  coordinates: { lat: 0, lng: 0 },
                  components: {},
                },
              ]);
              setIsLoading(false);
              return;
            }

            const results = await searchPlaces(newSearchValue);
            setSuggestions(results);
            console.log('Google Places results:', results);
          } catch (error) {
            console.error('Error fetching places:', error);
            setSuggestions([]);
          } finally {
            setIsLoading(false);
          }
        }, 300);
      } else {
        setSuggestions([]);
        setIsLoading(false);
      }
    },
    [isLoaded, searchPlaces],
  );

  // Handle location selection
  const handleLocationSelect = async (selectedValue: string) => {
    // Find the selected location from suggestions
    const location = suggestions.find((s) => s.placeId === selectedValue);
    if (!location) return;

    // Don't select fallback suggestions
    if (location.placeId === 'fallback') {
      return;
    }

    onChange?.(location.address);

    if (location.placeId && getPlaceDetails) {
      try {
        const fullDetails = await getPlaceDetails(location.placeId);
        if (fullDetails) {
          onLocationSelect?.(fullDetails);
        }
      } catch (error) {
        console.error('Error fetching place details:', error);
        onLocationSelect?.(location);
      }
    } else {
      onLocationSelect?.(location);
    }

    // Clear search and close popover after selection
    setSearchValue('');
    setIsOpen(false);
  };

  // Create the map pin icon
  const mapIcon = <MapPin className="h-4 w-4 opacity-50 flex-shrink-0" />;

  return (
    <BasePicker
      label={label}
      value={value}
      icon={mapIcon}
      disabled={disabled}
      error={error}
      className={className}
      containerClassName={containerClassName}
      contentClassName="w-[var(--radix-popover-trigger-width)] p-0"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <Command className="rounded-lg border shadow-md" shouldFilter={false}>
        <CommandInput
          placeholder={searchPlaceholder}
          value={searchValue}
          onValueChange={handleSearchChange}
        />
        <CommandList className="max-h-[300px]">
          {searchValue.length < 3 && !value ? (
            <div className="px-4 py-3 text-sm text-muted-foreground text-center">
              {hintMessage}
            </div>
          ) : !isLoading && suggestions.length === 0 ? (
            <CommandEmpty>{emptyMessage}</CommandEmpty>
          ) : null}
          <CommandGroup>
            {isLoading ? (
              <div className="px-4 py-3 text-sm text-muted-foreground flex items-center gap-3">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                <span>Searching locations...</span>
              </div>
            ) : (
              suggestions.map((suggestion) => (
                <CommandItem
                  key={suggestion.placeId}
                  value={suggestion.placeId}
                  onSelect={handleLocationSelect}
                >
                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                  {suggestion.address}
                </CommandItem>
              ))
            )}
          </CommandGroup>
        </CommandList>
      </Command>
    </BasePicker>
  );
}
