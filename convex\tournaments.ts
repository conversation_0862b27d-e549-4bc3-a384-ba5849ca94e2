import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Id } from './_generated/dataModel';

// Create a new draft tournament (minimal data required)
export const createDraftTournament = mutation({
  args: {
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get the current user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Find or create user
    let user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user) {
      // Create user if doesn't exist
      const userId = await ctx.db.insert('users', {
        clerkId: identity.subject,
        email: identity.email || '',
        firstName: identity.given_name,
        lastName: identity.family_name,
        imageUrl: identity.picture,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    }

    if (!user) {
      throw new Error('Failed to create or find user');
    }

    // Create the tournament with minimal required fields
    const tournamentId = await ctx.db.insert('tournaments', {
      name: args.name || 'Untitled Tournament',
      timeControlMinutes: 10, // Default values
      timeControlIncrement: 0,
      startDateTime: Date.now() + 86400000, // Default to tomorrow
      limitParticipants: false,
      isFideRated: false,
      limitByRating: false,
      equipment: 'organizer',
      status: 'draft',
      createdBy: user._id,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Create a default category
    await ctx.db.insert('tournament_categories', {
      tournamentId,
      name: 'Open',
      entryFee: 0,
      prizes: [
        { place: 1, amount: 0 },
        { place: 2, amount: 0 },
        { place: 3, amount: 0 },
      ],
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return tournamentId;
  },
});

// Update tournament basic information
export const updateTournamentBasics = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    name: v.optional(v.string()),
    format: v.optional(v.string()),
    description: v.optional(v.string()),
    bannerImageId: v.optional(v.id('_storage')),
    timeControlMinutes: v.optional(v.number()),
    timeControlIncrement: v.optional(v.number()),
    location: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    // Update only provided fields
    const updateData: any = { updatedAt: Date.now() };
    if (args.name !== undefined) updateData.name = args.name;
    if (args.format !== undefined) updateData.format = args.format;
    if (args.description !== undefined)
      updateData.description = args.description;
    if (args.bannerImageId !== undefined)
      updateData.bannerImageId = args.bannerImageId;
    if (args.timeControlMinutes !== undefined)
      updateData.timeControlMinutes = args.timeControlMinutes;
    if (args.timeControlIncrement !== undefined)
      updateData.timeControlIncrement = args.timeControlIncrement;
    if (args.location !== undefined) updateData.location = args.location;

    await ctx.db.patch(args.tournamentId, updateData);
    return args.tournamentId;
  },
});

// Update tournament schedule
export const updateTournamentSchedule = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    startDateTime: v.optional(v.number()),
    endDateTime: v.optional(v.number()),
    registrationDeadline: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    const updateData: any = { updatedAt: Date.now() };
    if (args.startDateTime !== undefined)
      updateData.startDateTime = args.startDateTime;
    if (args.endDateTime !== undefined)
      updateData.endDateTime = args.endDateTime;
    if (args.registrationDeadline !== undefined)
      updateData.registrationDeadline = args.registrationDeadline;

    await ctx.db.patch(args.tournamentId, updateData);
    return args.tournamentId;
  },
});

// Update tournament organizers
export const updateTournamentOrganizers = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    organizerName: v.optional(v.string()),
    organizerEmail: v.optional(v.string()),
    organizerPhone: v.optional(v.string()),
    organizerSocialLinks: v.optional(v.string()),
    arbiterName: v.optional(v.string()),
    arbiterContact: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    const updateData: any = { updatedAt: Date.now() };
    if (args.organizerName !== undefined)
      updateData.organizerName = args.organizerName;
    if (args.organizerEmail !== undefined)
      updateData.organizerEmail = args.organizerEmail;
    if (args.organizerPhone !== undefined)
      updateData.organizerPhone = args.organizerPhone;
    if (args.organizerSocialLinks !== undefined)
      updateData.organizerSocialLinks = args.organizerSocialLinks;
    if (args.arbiterName !== undefined)
      updateData.arbiterName = args.arbiterName;
    if (args.arbiterContact !== undefined)
      updateData.arbiterContact = args.arbiterContact;

    await ctx.db.patch(args.tournamentId, updateData);
    return args.tournamentId;
  },
});

// Update tournament settings
export const updateTournamentSettings = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    limitParticipants: v.optional(v.boolean()),
    maxParticipants: v.optional(v.number()),
    isFideRated: v.optional(v.boolean()),
    limitByRating: v.optional(v.boolean()),
    ratingLimit: v.optional(v.number()),
    ratingType: v.optional(v.union(v.literal('local'), v.literal('fide'))),
    equipment: v.optional(
      v.union(v.literal('organizer'), v.literal('players'), v.literal('mixed')),
    ),
    additionalInfo: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    const updateData: any = { updatedAt: Date.now() };
    if (args.limitParticipants !== undefined)
      updateData.limitParticipants = args.limitParticipants;
    if (args.maxParticipants !== undefined)
      updateData.maxParticipants = args.maxParticipants;
    if (args.isFideRated !== undefined)
      updateData.isFideRated = args.isFideRated;
    if (args.limitByRating !== undefined)
      updateData.limitByRating = args.limitByRating;
    if (args.ratingLimit !== undefined)
      updateData.ratingLimit = args.ratingLimit;
    if (args.ratingType !== undefined) updateData.ratingType = args.ratingType;
    if (args.equipment !== undefined) updateData.equipment = args.equipment;
    if (args.additionalInfo !== undefined)
      updateData.additionalInfo = args.additionalInfo;

    await ctx.db.patch(args.tournamentId, updateData);
    return args.tournamentId;
  },
});

// Update tournament categories
export const updateTournamentCategories = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    categories: v.array(
      v.object({
        id: v.optional(v.id('tournament_categories')), // For existing categories
        name: v.string(),
        entryFee: v.number(),
        prizes: v.array(
          v.object({
            place: v.number(),
            amount: v.number(),
          }),
        ),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    // Get existing categories
    const existingCategories = await ctx.db
      .query('tournament_categories')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    // Track which categories to keep
    const categoriesToKeep = new Set();

    // Update or create categories
    for (const category of args.categories) {
      if (category.id) {
        // Update existing category
        await ctx.db.patch(category.id, {
          name: category.name,
          entryFee: category.entryFee,
          prizes: category.prizes,
          updatedAt: Date.now(),
        });
        categoriesToKeep.add(category.id);
      } else {
        // Create new category
        const newCategoryId = await ctx.db.insert('tournament_categories', {
          tournamentId: args.tournamentId,
          name: category.name,
          entryFee: category.entryFee,
          prizes: category.prizes,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        categoriesToKeep.add(newCategoryId);
      }
    }

    // Delete categories that are no longer needed
    for (const existingCategory of existingCategories) {
      if (!categoriesToKeep.has(existingCategory._id)) {
        await ctx.db.delete(existingCategory._id);
      }
    }

    return args.tournamentId;
  },
});

// Update tournament sponsors
export const updateTournamentSponsors = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    sponsors: v.array(
      v.object({
        id: v.optional(v.id('tournament_sponsors')), // For existing sponsors
        name: v.string(),
        logoImageId: v.optional(v.id('_storage')),
        website: v.optional(v.string()),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    // Get existing sponsors
    const existingSponsors = await ctx.db
      .query('tournament_sponsors')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    // Track which sponsors to keep
    const sponsorsToKeep = new Set();

    // Update or create sponsors
    for (const sponsor of args.sponsors) {
      if (sponsor.id) {
        // Update existing sponsor
        await ctx.db.patch(sponsor.id, {
          name: sponsor.name,
          logoImageId: sponsor.logoImageId,
          website: sponsor.website,
          updatedAt: Date.now(),
        });
        sponsorsToKeep.add(sponsor.id);
      } else {
        // Create new sponsor
        const newSponsorId = await ctx.db.insert('tournament_sponsors', {
          tournamentId: args.tournamentId,
          name: sponsor.name,
          logoImageId: sponsor.logoImageId,
          website: sponsor.website,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        sponsorsToKeep.add(newSponsorId);
      }
    }

    // Delete sponsors that are no longer needed
    for (const existingSponsor of existingSponsors) {
      if (!sponsorsToKeep.has(existingSponsor._id)) {
        await ctx.db.delete(existingSponsor._id);
      }
    }

    return args.tournamentId;
  },
});

// Create a new tournament (legacy - for complete data)
export const createTournament = mutation({
  args: {
    // Basic tournament information
    name: v.string(),
    format: v.optional(v.string()),
    description: v.optional(v.string()),
    bannerImageId: v.optional(v.id('_storage')),

    // Time control
    timeControlMinutes: v.number(),
    timeControlIncrement: v.number(),

    // Location
    location: v.optional(v.string()),

    // Schedule
    startDateTime: v.number(),
    endDateTime: v.optional(v.number()),
    registrationDeadline: v.optional(v.number()),

    // Organizer information
    organizerName: v.optional(v.string()),
    organizerEmail: v.optional(v.string()),
    organizerPhone: v.optional(v.string()),
    organizerSocialLinks: v.optional(v.string()),

    // Chief arbiter information
    arbiterName: v.optional(v.string()),
    arbiterContact: v.optional(v.string()),

    // Settings
    limitParticipants: v.boolean(),
    maxParticipants: v.optional(v.number()),
    isFideRated: v.boolean(),
    limitByRating: v.boolean(),
    ratingLimit: v.optional(v.number()),
    ratingType: v.optional(v.union(v.literal('local'), v.literal('fide'))),
    equipment: v.union(
      v.literal('organizer'),
      v.literal('players'),
      v.literal('mixed'),
    ),
    additionalInfo: v.optional(v.string()),

    // Categories
    categories: v.array(
      v.object({
        name: v.string(),
        entryFee: v.number(),
        prizes: v.array(
          v.object({
            place: v.number(),
            amount: v.number(),
          }),
        ),
      }),
    ),

    // Sponsors
    sponsors: v.optional(
      v.array(
        v.object({
          name: v.string(),
          logoImageId: v.optional(v.id('_storage')),
          website: v.optional(v.string()),
        }),
      ),
    ),
  },
  handler: async (ctx, args) => {
    // Get the current user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Find or create user
    let user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user) {
      // Create user if doesn't exist
      const userId = await ctx.db.insert('users', {
        clerkId: identity.subject,
        email: identity.email || '',
        firstName: identity.given_name,
        lastName: identity.family_name,
        imageUrl: identity.picture,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    }

    if (!user) {
      throw new Error('Failed to create or find user');
    }

    // Create the tournament
    const tournamentId = await ctx.db.insert('tournaments', {
      name: args.name,
      format: args.format,
      description: args.description,
      bannerImageId: args.bannerImageId,
      timeControlMinutes: args.timeControlMinutes,
      timeControlIncrement: args.timeControlIncrement,
      location: args.location,
      startDateTime: args.startDateTime,
      endDateTime: args.endDateTime,
      registrationDeadline: args.registrationDeadline,
      organizerName: args.organizerName,
      organizerEmail: args.organizerEmail,
      organizerPhone: args.organizerPhone,
      organizerSocialLinks: args.organizerSocialLinks,
      arbiterName: args.arbiterName,
      arbiterContact: args.arbiterContact,
      limitParticipants: args.limitParticipants,
      maxParticipants: args.maxParticipants,
      isFideRated: args.isFideRated,
      limitByRating: args.limitByRating,
      ratingLimit: args.ratingLimit,
      ratingType: args.ratingType,
      equipment: args.equipment,
      additionalInfo: args.additionalInfo,
      status: 'draft',
      createdBy: user._id,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Create categories
    for (const category of args.categories) {
      await ctx.db.insert('tournament_categories', {
        tournamentId,
        name: category.name,
        entryFee: category.entryFee,
        prizes: category.prizes,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    // Create sponsors if provided
    if (args.sponsors) {
      for (const sponsor of args.sponsors) {
        await ctx.db.insert('tournament_sponsors', {
          tournamentId,
          name: sponsor.name,
          logoImageId: sponsor.logoImageId,
          website: sponsor.website,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      }
    }

    return tournamentId;
  },
});

// Get tournaments created by the current user
export const getUserTournaments = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user) {
      return [];
    }

    const tournaments = await ctx.db
      .query('tournaments')
      .withIndex('by_creator', (q) => q.eq('createdBy', user._id))
      .order('desc')
      .collect();

    return tournaments;
  },
});

// Get a single tournament with its categories and sponsors
export const getTournament = query({
  args: { tournamentId: v.id('tournaments') },
  handler: async (ctx, args) => {
    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      return null;
    }

    const categories = await ctx.db
      .query('tournament_categories')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    const sponsors = await ctx.db
      .query('tournament_sponsors')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    const creator = await ctx.db.get(tournament.createdBy);

    return {
      ...tournament,
      categories,
      sponsors,
      creator,
    };
  },
});

// Get all published tournaments (public)
export const getPublishedTournaments = query({
  handler: async (ctx) => {
    const tournaments = await ctx.db
      .query('tournaments')
      .withIndex('by_status', (q) => q.eq('status', 'published'))
      .order('desc')
      .collect();

    return tournaments;
  },
});

// Update tournament status
export const updateTournamentStatus = mutation({
  args: {
    tournamentId: v.id('tournaments'),
    status: v.union(
      v.literal('draft'),
      v.literal('published'),
      v.literal('ongoing'),
      v.literal('completed'),
      v.literal('cancelled'),
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to update this tournament');
    }

    await ctx.db.patch(args.tournamentId, {
      status: args.status,
      updatedAt: Date.now(),
    });

    return args.tournamentId;
  },
});

// Delete tournament
export const deleteTournament = mutation({
  args: { tournamentId: v.id('tournaments') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const tournament = await ctx.db.get(args.tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    // Check if user is the creator
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', identity.subject))
      .first();

    if (!user || tournament.createdBy !== user._id) {
      throw new Error('Not authorized to delete this tournament');
    }

    // Delete related categories
    const categories = await ctx.db
      .query('tournament_categories')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    for (const category of categories) {
      await ctx.db.delete(category._id);
    }

    // Delete related sponsors
    const sponsors = await ctx.db
      .query('tournament_sponsors')
      .withIndex('by_tournament', (q) =>
        q.eq('tournamentId', args.tournamentId),
      )
      .collect();

    for (const sponsor of sponsors) {
      await ctx.db.delete(sponsor._id);
    }

    // Delete the tournament
    await ctx.db.delete(args.tournamentId);

    return args.tournamentId;
  },
});
