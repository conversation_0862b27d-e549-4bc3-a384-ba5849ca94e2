# Google Places API (New) Integration Guide

This guide will help you integrate the new Google Places API with the LocationField component for address autocomplete functionality.

## Prerequisites

1. **Google Cloud Console Account**: You need a Google Cloud Console account
2. **Google Places API Key**: You'll need to enable the Places API (New) and get an API key
3. **Domain Configuration**: Configure your domain for the API key

## Step 1: Set Up Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Places API (New)** - This is the new version that we're using
   - Geocoding API (optional, for coordinate conversion)

**Important**: Make sure to enable "Places API (New)" and NOT the legacy "Places API".

## Step 2: Get API Key

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API Key"
3. Copy the API key
4. Click "Restrict Key" to configure restrictions:
   - **Application restrictions**: HTTP referrers
   - **Website restrictions**: Add your domain (e.g., `localhost:3000/*`, `yourdomain.com/*`)
   - **API restrictions**: Select the APIs you enabled

## Step 3: Environment Configuration

Add your API key to your environment variables:

```bash
# .env.local
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_api_key_here
```

## Step 4: Install Dependencies

The new Places API (New) uses REST API calls, so we don't need to install any Google Maps JavaScript libraries. The implementation uses the native `fetch` API.

## Step 5: Create Google Places Hook

Create a custom hook for Google Places integration using the new REST API:

```typescript
// src/hooks/use-google-places.ts
import { useEffect, useState } from 'react';

export interface PlaceResult {
  address: string;
  placeId: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  components: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export function useGooglePlaces() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;

    if (!apiKey) {
      console.warn(
        'Google Places API key not found. Please add NEXT_PUBLIC_GOOGLE_PLACES_API_KEY to your environment variables.',
      );
      return;
    }

    console.log('Google Places API (New) ready - using REST API');
    setIsLoaded(true);
  }, []);

  const searchPlaces = async (input: string): Promise<PlaceResult[]> => {
    if (!isLoaded || !input) return [];

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;
    if (!apiKey) return [];

    try {
      const response = await fetch(
        'https://places.googleapis.com/v1/places:autocomplete',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask':
              'suggestions.placePrediction.place,suggestions.placePrediction.placeId,suggestions.placePrediction.text',
          },
          body: JSON.stringify({
            input,
            locationBias: {
              circle: {
                center: {
                  latitude: 14.5995, // Philippines center
                  longitude: 120.9842,
                },
                radius: 50000.0, // 50km radius
              },
            },
            includedRegionCodes: ['ph'], // Restrict to Philippines
            languageCode: 'en',
          }),
        },
      );

      const data = await response.json();

      if (data.suggestions) {
        return data.suggestions
          .filter((suggestion: any) => suggestion.placePrediction)
          .map((suggestion: any) => ({
            address: suggestion.placePrediction.text.text,
            placeId: suggestion.placePrediction.placeId,
            coordinates: { lat: 0, lng: 0 },
            components: {},
          }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching places:', error);
      return [];
    }
  };

  const getPlaceDetails = async (
    placeId: string,
  ): Promise<PlaceResult | null> => {
    if (!placesService) return null;

    return new Promise((resolve) => {
      placesService.getDetails(
        {
          placeId,
          fields: ['formatted_address', 'geometry', 'address_components'],
        },
        (place, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && place) {
            const components: PlaceResult['components'] = {};

            place.address_components?.forEach((component) => {
              const types = component.types;
              if (types.includes('street_number') || types.includes('route')) {
                components.street =
                  (components.street || '') + ' ' + component.long_name;
              } else if (types.includes('locality')) {
                components.city = component.long_name;
              } else if (types.includes('administrative_area_level_1')) {
                components.state = component.short_name;
              } else if (types.includes('country')) {
                components.country = component.long_name;
              } else if (types.includes('postal_code')) {
                components.postalCode = component.long_name;
              }
            });

            resolve({
              address: place.formatted_address || '',
              placeId,
              coordinates: {
                lat: place.geometry?.location?.lat() || 0,
                lng: place.geometry?.location?.lng() || 0,
              },
              components,
            });
          } else {
            resolve(null);
          }
        },
      );
    });
  };

  return {
    isLoaded,
    searchPlaces,
    getPlaceDetails,
  };
}
```

## Step 6: Update LocationField Component

Update the LocationField component to use Google Places:

```typescript
// In src/components/common/location-field.tsx
// Add this import at the top
import { useGooglePlaces } from '@/hooks/use-google-places';

// Replace the handleInputChange function with:
const { isLoaded, searchPlaces, getPlaceDetails } = useGooglePlaces();

const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  const newValue = e.target.value;
  setInputValue(newValue);
  onChange?.(newValue);

  if (newValue.length > 2 && isLoaded) {
    setShowSuggestions(true);
    try {
      const results = await searchPlaces(newValue);
      setSuggestions(results);
    } catch (error) {
      console.error('Error fetching places:', error);
      setSuggestions([]);
    }
  } else {
    setShowSuggestions(false);
    setSuggestions([]);
  }
};

// Update handleSuggestionSelect to get full place details:
const handleSuggestionSelect = async (location: LocationResult) => {
  setInputValue(location.address);
  onChange?.(location.address);

  if (location.placeId && getPlaceDetails) {
    try {
      const fullDetails = await getPlaceDetails(location.placeId);
      if (fullDetails) {
        onLocationSelect?.(fullDetails);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
    }
  }

  setShowSuggestions(false);
  setSuggestions([]);
};
```

## Step 7: Add TypeScript Definitions

Add Google Maps types to your project:

```bash
pnpm add @types/google.maps
```

## Step 8: Usage Example

The LocationField is now ready to use with Google Places integration:

```typescript
const [location, setLocation] = useState<string>('');
const [selectedLocation, setSelectedLocation] = useState<LocationResult | null>(
  null,
);

<LocationField
  label="Location"
  value={location}
  onChange={setLocation}
  onLocationSelect={(location) => {
    setSelectedLocation(location);
    console.log('Selected location:', location);
  }}
/>;
```

## Security Considerations

1. **API Key Restrictions**: Always restrict your API key to specific domains
2. **Rate Limiting**: Implement debouncing to avoid excessive API calls
3. **Error Handling**: Handle API errors gracefully
4. **Billing**: Monitor your API usage to avoid unexpected charges

## Optional Enhancements

1. **Debouncing**: Add debouncing to reduce API calls
2. **Caching**: Cache recent searches
3. **Geolocation**: Add current location detection
4. **Map Integration**: Show selected location on a map
5. **Custom Styling**: Customize the appearance of suggestions

## Troubleshooting

### Common Issues and Solutions

1. **API Key Issues**

   - Check browser console for authentication errors
   - Verify API key is correctly set in `.env.local`
   - Ensure API key has proper permissions

2. **CORS Errors**

   - Add your domain (localhost:3000) to API key restrictions in Google Cloud Console
   - For development, you can temporarily remove domain restrictions

3. **No Suggestions Appearing**

   - Verify the Places API is enabled in Google Cloud Console
   - Check console logs for "Google Places API loaded successfully"
   - Ensure you're typing at least 3 characters

4. **Billing Issues**

   - Ensure billing is enabled for your Google Cloud project
   - Check if you've exceeded your free tier limits

5. **API Not Loading**
   - Check network tab for failed requests to Google APIs
   - Verify internet connection
   - Check if any ad blockers are interfering

### Debug Console Messages

Look for these messages in the browser console:

- ✅ `Google Places API Key: Present`
- ✅ `Loading Google Places API...`
- ✅ `Google Places API loaded successfully`
- ✅ `Searching for places with input: [your search]`
- ✅ `Google Places results: [array of results]`

### Testing Steps

1. Open browser developer tools (F12)
2. Go to Console tab
3. Visit `/test-location` page
4. Type in the location field
5. Watch for debug messages and API responses

This integration will provide a professional address autocomplete experience similar to what users expect from modern web applications.
