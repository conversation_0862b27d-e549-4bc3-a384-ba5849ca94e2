'use client';

import * as React from 'react';
import { CheckIcon, ChevronDownIcon, Plus } from 'lucide-react';

import { cn } from '@/lib/utils';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { BasePicker } from './base-picker';

export interface ComboboxOption {
  value: string;
  label: string;
}

export interface ComboboxProps {
  value?: string;
  onValueChange: (value: string) => void;
  options: ComboboxOption[];
  label: string;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
  searchable?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  allowCustom?: boolean;
  customOptionLabel?: string;
}

export function Combobox({
  value,
  onValueChange,
  options,
  label,
  searchPlaceholder = 'Search...',
  emptyMessage = 'No options found.',
  disabled = false,
  error,
  className,
  containerClassName,
  searchable = true,
  icon,
  iconPosition = 'left',
  allowCustom = false,
  customOptionLabel = 'Create',
}: ComboboxProps) {
  const [searchValue, setSearchValue] = React.useState('');
  const [isOpen, setIsOpen] = React.useState(false);

  // Find the selected option
  const selectedOption = options.find((option) => option.value === value);
  const displayValue = selectedOption ? selectedOption.label : '';

  // Filter options based on search
  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchValue) return options;

    return options.filter(
      (option) =>
        option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
        option.value.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [options, searchValue, searchable]);

  // Check if we should show custom option
  const shouldShowCustomOption = React.useMemo(() => {
    if (!allowCustom || !searchValue.trim()) return false;

    // Don't show if exact match exists
    const exactMatch = options.some(
      (option) =>
        option.label.toLowerCase() === searchValue.toLowerCase() ||
        option.value.toLowerCase() === searchValue.toLowerCase(),
    );

    return !exactMatch;
  }, [allowCustom, searchValue, options]);

  // Handle option selection
  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue);
    setSearchValue('');
    setIsOpen(false);
  };

  // Use provided icon or default to chevron
  const displayIcon = icon || (
    <ChevronDownIcon className="h-4 w-4 opacity-50 flex-shrink-0" />
  );

  return (
    <BasePicker
      label={label}
      value={displayValue}
      icon={displayIcon}
      iconPosition={iconPosition}
      disabled={disabled}
      error={error}
      className={className}
      containerClassName={containerClassName}
      contentClassName="w-[var(--radix-popover-trigger-width)] p-0"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <Command className="rounded-lg border shadow-md w-full">
        {searchable && (
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
        )}
        <CommandList className="max-h-[300px]">
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          <CommandGroup>
            {filteredOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={handleSelect}
              >
                <CheckIcon
                  className={cn(
                    'mr-2 h-4 w-4',
                    value === option.value ? 'opacity-100' : 'opacity-0',
                  )}
                />
                {option.label}
              </CommandItem>
            ))}
            {shouldShowCustomOption && (
              <CommandItem
                key={`custom-${searchValue}`}
                value={searchValue}
                onSelect={handleSelect}
                className="text-primary"
              >
                <Plus className="mr-2 h-4 w-4" />
                {customOptionLabel} &ldquo;{searchValue}&rdquo;
              </CommandItem>
            )}
          </CommandGroup>
        </CommandList>
      </Command>
    </BasePicker>
  );
}
