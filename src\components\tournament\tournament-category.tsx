'use client';

import * as React from 'react';
import { Input } from '@/components/common/input';
import { CategorySelector } from './category-selector';
import { PrizeDistribution, type PrizePlace } from './prize-distribution';
import { cn } from '@/lib/utils';
import { DollarSign } from 'lucide-react';

export interface TournamentCategoryData {
  id: string;
  name: string;
  entryFee: string;
  prizes: PrizePlace[];
}

export interface TournamentCategoryProps {
  category: TournamentCategoryData;
  onCategoryChange: (category: TournamentCategoryData) => void;
  className?: string;
}

export function TournamentCategory({
  category,
  onCategoryChange,
  className,
}: TournamentCategoryProps) {
  const handleNameChange = (name: string) => {
    onCategoryChange({
      ...category,
      name,
    });
  };

  const handleEntryFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCategoryChange({
      ...category,
      entryFee: e.target.value,
    });
  };

  const handlePrizesChange = (prizes: PrizePlace[]) => {
    onCategoryChange({
      ...category,
      prizes,
    });
  };

  return (
    <div className={cn('space-y-6 pt-4', className)}>
      <div className="flex gap-6">
        {/* Category Selection */}
        <CategorySelector
          value={category.name}
          onValueChange={handleNameChange}
          label="Category"
        />

        {/* Entry Fee */}
        <Input
          label="Entry Fee (₱)"
          type="number"
          value={category.entryFee}
          onChange={handleEntryFeeChange}
          placeholder="0"
          min="0"
          step="1"
          icon={<DollarSign className="h-4 w-4" />}
        />
      </div>

      {/* Prize Distribution */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">
          Prize Distribution
        </h4>
        <PrizeDistribution
          prizes={category.prizes}
          onPrizesChange={handlePrizesChange}
        />
      </div>
    </div>
  );
}
