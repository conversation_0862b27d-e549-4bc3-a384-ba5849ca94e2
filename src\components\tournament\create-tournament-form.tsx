'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useTournaments } from '@/hooks/use-tournaments';
import { convertFormToTournamentData, validateTournamentData } from '@/lib/tournament-utils';
import { type TournamentCategoryData } from '@/components/tournament/tournament-category';
import { type TimeControlValue } from '@/components/common/time-control';

interface CreateTournamentFormProps {
  // All the form state from the create tournament page
  tournamentName: string;
  tournamentFormat: string;
  description: string;
  timeControl: TimeControlValue;
  location: string;
  startDateTime?: Date;
  endDateTime?: Date;
  registrationDeadline?: Date;
  organizerName: string;
  organizerEmail: string;
  organizerPhone: string;
  organizerSocialLinks: string;
  arbiterName: string;
  arbiterContact: string;
  limitParticipants: boolean;
  maxParticipants: string;
  isFideRated: boolean;
  limitByRating: boolean;
  ratingLimit: string;
  ratingType: string;
  equipment: string;
  additionalInfo: string;
  categories: TournamentCategoryData[];
  sponsors: Array<{
    id: string;
    name: string;
    logo: File | null;
    website: string;
  }>;
  onSuccess?: () => void;
}

export function CreateTournamentForm(props: CreateTournamentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { createTournament } = useTournaments();

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Convert form data to database format
      const tournamentData = convertFormToTournamentData(props);

      // Validate the data
      const validationErrors = validateTournamentData(tournamentData);
      if (validationErrors.length > 0) {
        toast({
          title: 'Validation Error',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      // Create the tournament
      const tournamentId = await createTournament(tournamentData);

      toast({
        title: 'Tournament Created',
        description: 'Your tournament has been created successfully!',
      });

      // Call success callback if provided
      props.onSuccess?.();

      console.log('Tournament created with ID:', tournamentId);
    } catch (error) {
      console.error('Error creating tournament:', error);
      toast({
        title: 'Error',
        description: 'Failed to create tournament. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Button
      onClick={handleSubmit}
      disabled={isSubmitting}
      className="w-full py-8 text-lg font-semibold"
      size="lg"
    >
      {isSubmitting ? 'Creating Tournament...' : 'Create Tournament'}
    </Button>
  );
}

// Example usage in the create tournament page:
/*
// In your create-tournament page.tsx, replace the existing button with:

<CreateTournamentForm
  tournamentName={tournamentName}
  tournamentFormat={tournamentFormat}
  description={description}
  timeControl={timeControl}
  location={location}
  startDateTime={startDateTime}
  endDateTime={endDateTime}
  registrationDeadline={registrationDeadline}
  organizerName={organizerName}
  organizerEmail={organizerEmail}
  organizerPhone={organizerPhone}
  organizerSocialLinks={organizerSocialLinks}
  arbiterName={arbiterName}
  arbiterContact={arbiterContact}
  limitParticipants={limitParticipants}
  maxParticipants={maxParticipants}
  isFideRated={isFideRated}
  limitByRating={limitByRating}
  ratingLimit={ratingLimit}
  ratingType={ratingType}
  equipment={equipment}
  additionalInfo={additionalInfo}
  categories={categories}
  sponsors={sponsors}
  onSuccess={() => {
    // Redirect to tournament list or show success message
    router.push('/dashboard');
  }}
/>
*/
