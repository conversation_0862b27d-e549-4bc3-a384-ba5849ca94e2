'use client';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { ConvexClientProvider } from '@/components/providers/convex';
import { Toaster } from '@/components/ui/sonner';

export const Providers = ({ children }: { children: React.ReactNode }) => {
  return (
    <ClerkProvider>
      <ConvexClientProvider>
        {children}
        <Toaster />
      </ConvexClientProvider>
    </ClerkProvider>
  );
};
