'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { ClockIcon } from 'lucide-react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { BasePicker } from './base-picker';

export interface TimePickerProps {
  time: Date | undefined;
  onTimeChange: (time: Date | undefined) => void;
  label: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
  format?: string;
  clearable?: boolean;
  step?: number; // Step in minutes
}

export function TimePicker({
  time,
  onTimeChange,
  label,
  disabled = false,
  error,
  className,
  containerClassName,
  format: timeFormat = 'h:mm a', // Default format: "3:30 PM"
  step = 30, // Default 30 minute intervals
}: TimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');

  // Format the time for display
  const formattedTime = time ? format(time, timeFormat) : '';

  // Create the clock icon with consistent styling
  const clockIcon = <ClockIcon className="h-4 w-4 opacity-50 flex-shrink-0" />;

  // Generate time options
  const timeOptions = React.useMemo(() => {
    const options = [];
    const totalMinutesInDay = 24 * 60;

    for (let minutes = 0; minutes < totalMinutesInDay; minutes += step) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;

      const date = new Date();
      date.setHours(hours, mins, 0, 0);

      options.push({
        value: format(date, 'HH:mm'),
        label: format(date, timeFormat),
        date,
      });
    }

    return options;
  }, [step, timeFormat]);

  const handleTimeSelect = (value: string) => {
    const selectedTime = timeOptions.find((option) => option.value === value);
    if (selectedTime) {
      onTimeChange(selectedTime.date);
    }
    setIsOpen(false);
  };

  // Filter time options based on search
  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return timeOptions;
    return timeOptions.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [timeOptions, searchValue]);

  return (
    <BasePicker
      label={label}
      value={formattedTime}
      icon={clockIcon}
      disabled={disabled}
      error={error}
      className={className}
      containerClassName={containerClassName}
      contentClassName="w-[var(--radix-popover-trigger-width)] p-0"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <Command className="rounded-lg border shadow-md w-full">
        <CommandInput
          placeholder="Search time..."
          value={searchValue}
          onValueChange={setSearchValue}
        />
        <CommandList className="max-h-[300px]">
          <CommandEmpty>No time found.</CommandEmpty>
          <CommandGroup>
            {filteredOptions.map((option) => (
              <CommandItem
                key={option.value}
                value={option.value}
                onSelect={handleTimeSelect}
              >
                {option.label}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </Command>
    </BasePicker>
  );
}
