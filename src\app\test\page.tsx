import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, XCircle } from 'lucide-react';

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 text-gray-800 p-6 md:p-12">
      <div className="max-w-4xl mx-auto text-center space-y-8">
        <h1 className="text-4xl md:text-5xl font-bold text-primary">
          Grow Your Chess Tournaments — Without the Admin Headaches
        </h1>
        <p className="text-lg md:text-xl text-muted-foreground">
          <strong>Chessnam</strong> helps tournament organizers in the
          Philippines list, manage, and fill their events faster.
        </p>

        <Card className="bg-white shadow-md border rounded-2xl">
          <CardContent className="p-6 grid md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">
                ❌ Without Chessnam
              </h2>
              <ul className="space-y-2 text-left">
                <li className="flex items-center gap-2">
                  <XCircle className="text-red-500 w-5 h-5" /> Low player
                  turnout and limited reach
                </li>
                <li className="flex items-center gap-2">
                  <XCircle className="text-red-500 w-5 h-5" /> Manually tracking
                  payments and registrations
                </li>
                <li className="flex items-center gap-2">
                  <XCircle className="text-red-500 w-5 h-5" /> Last-minute
                  dropouts, hard to manage updates
                </li>
              </ul>
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-4">✅ With Chessnam</h2>
              <ul className="space-y-2 text-left">
                <li className="flex items-center gap-2">
                  <CheckCircle className="text-green-600 w-5 h-5" />{' '}
                  Discoverable by verified players nationwide
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="text-green-600 w-5 h-5" /> Instant
                  online registration and payment
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="text-green-600 w-5 h-5" /> Automated
                  reminders and event updates
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <div className="text-left space-y-4">
          <h2 className="text-2xl font-semibold">
            Key Features for Organizers
          </h2>
          <ul className="list-disc list-inside text-base md:text-lg space-y-1">
            <li>Create tournament listings in minutes</li>
            <li>Customize entry fees, format, and time controls</li>
            <li>Accept GCash, Maya, bank transfers, or cards</li>
            <li>Send SMS/email updates to registered players</li>
            <li>Track registrations and check-in easily on event day</li>
          </ul>
        </div>

        <div className="text-center py-8">
          <h3 className="text-xl md:text-2xl font-bold mb-4">
            Join Chessnam’s Early Organizer Program
          </h3>
          <p className="mb-6 text-muted-foreground">
            Free to join. Limited slots available during beta.
          </p>
          <Button className="text-lg px-6 py-3 rounded-full">
            Apply for Early Access
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
