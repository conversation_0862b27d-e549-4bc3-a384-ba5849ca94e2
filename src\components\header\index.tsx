'use client';

import { NavItems } from '@/components/header/nav-items';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SignInButton, SignedOut, SignOutButton, useUser } from '@clerk/nextjs';
import { getUserInitials } from '@/lib/user-utils';
import { Plus } from 'lucide-react';
import Link from 'next/link';
import {
  IconCreditCard,
  IconLogout,
  IconSettings,
  IconUserCircle,
} from '@tabler/icons-react';

export const Header = () => {
  const { isLoaded, isSignedIn, user } = useUser();

  if (!isLoaded) {
    return null;
  }

  console.log('user', user);
  return (
    <header className="shadow-md sticky top-0 z-20 flex justify-between w-full h-16 items-center gap-4 bg-background px-4 md:px-6">
      <NavItems />

      <div className="flex items-center gap-4 md:gap-2 lg:gap-4">
        {isSignedIn && user ? (
          <>
            <Button variant="outline" asChild>
              <Link href="/create-tournament">
                <Plus className="size-4 mr-1" /> Create Tournament
              </Link>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="relative h-8 w-8 rounded-full"
                >
                  <Avatar className="h-9 w-9">
                    <AvatarImage
                      src={user.imageUrl || ''}
                      alt={user.fullName || ''}
                    />
                    <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user.fullName}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user.primaryEmailAddress?.emailAddress}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem asChild>
                    <Link href="/account/profile">
                      <IconUserCircle className="mr-2" />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/payment">
                      <IconCreditCard className="mr-2" />
                      Payments
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/settings">
                      <IconSettings className="mr-2" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="cursor-pointer">
                  <IconLogout />
                  <SignOutButton />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        ) : (
          <div className="space-x-2">
            <SignedOut>
              <Button
                variant="outline"
                className="p-5 hover:border-primary hover:text-primary cursor-pointer"
                asChild
              >
                <SignInButton forceRedirectUrl={'/create-tournament'} />
              </Button>

              <Button className="p-5 cursor-pointer" asChild>
                <SignInButton forceRedirectUrl={'/create-tournament'}>
                  Get Started
                </SignInButton>
              </Button>
            </SignedOut>
          </div>
        )}
      </div>
    </header>
  );
};
